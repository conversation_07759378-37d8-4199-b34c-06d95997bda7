import { Center, Loader, Stack, Text } from '@mantine/core';

interface LoadingProps {
  message?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  fullHeight?: boolean;
}

export function Loading({ message = 'Loading...', size = 'lg', fullHeight = true }: LoadingProps) {
  return (
    <Center h={fullHeight ? '100vh' : '200px'}>
      <Stack align="center" gap="md">
        <Loader size={size} />
        <Text c="dimmed" size="sm">
          {message}
        </Text>
      </Stack>
    </Center>
  );
}
