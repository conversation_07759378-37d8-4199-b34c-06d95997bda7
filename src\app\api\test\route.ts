import { NextResponse } from 'next/server';
import { testDatabaseConnection, testTableStructure } from '@/lib/test-db';

export async function GET() {
  try {
    const connectionTest = await testDatabaseConnection();
    const tableTest = await testTableStructure();

    return NextResponse.json({
      success: connectionTest && tableTest,
      tests: {
        databaseConnection: connectionTest,
        tableStructure: tableTest,
      },
      message: connectionTest && tableTest 
        ? 'All tests passed! Database is ready.' 
        : 'Some tests failed. Check the logs.',
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Test execution failed',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
