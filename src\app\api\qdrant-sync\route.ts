import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { QdrantClient } from '@qdrant/js-client-rest';

// Initialize Qdrant client
const qdrantClient = new QdrantClient({
  url: process.env.QDRANT_URL!,
  apiKey: process.env.QDRANT_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    const body = await request.json();
    const { question, answer, vector, collection_id, user_id } = body;

    // Validate required fields
    if (!question || !answer || !vector || !collection_id || !user_id) {
      return NextResponse.json(
        { error: 'Missing required fields: question, answer, vector, collection_id, user_id' },
        { status: 400 }
      );
    }

    // Ensure the user can only sync their own data
    if (user.id !== user_id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Validate vector format
    if (!Array.isArray(vector) || vector.length === 0) {
      return NextResponse.json(
        { error: 'Vector must be a non-empty array' },
        { status: 400 }
      );
    }

    // Create collection name for Qdrant (using user_id and collection_id)
    const qdrantCollectionName = `user_${user_id}_collection_${collection_id}`;

    try {
      // Check if collection exists, create if it doesn't
      const collections = await qdrantClient.getCollections();
      const collectionExists = collections.collections?.some(
        (col) => col.name === qdrantCollectionName
      );

      if (!collectionExists) {
        await qdrantClient.createCollection(qdrantCollectionName, {
          vectors: {
            size: vector.length,
            distance: 'Cosine',
          },
        });
      }

      // Create a unique point ID based on question content
      const pointId = Buffer.from(`${collection_id}_${question}`).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 32);

      // Upsert the vector point
      await qdrantClient.upsert(qdrantCollectionName, {
        wait: true,
        points: [
          {
            id: pointId,
            vector: vector,
            payload: {
              question,
              answer,
              collection_id,
              user_id,
              created_at: new Date().toISOString(),
            },
          },
        ],
      });

      return NextResponse.json({
        success: true,
        message: 'Vector synced to Qdrant successfully',
        pointId,
        collectionName: qdrantCollectionName,
      });

    } catch (qdrantError) {
      console.error('Qdrant operation failed:', qdrantError);
      return NextResponse.json(
        { error: 'Failed to sync with Qdrant', details: qdrantError instanceof Error ? qdrantError.message : 'Unknown error' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in qdrant-sync API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
