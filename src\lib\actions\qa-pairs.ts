'use server';

import { revalidatePath } from 'next/cache';
import { supabase } from '@/lib/supabase';
import { getCurrentUser } from '@/lib/auth';

export async function createQAPair(data: {
  question: string;
  answer: string;
  collection_id: string;
}) {
  try {
    const user = await getCurrentUser();
    
    // Verify the collection belongs to the current user
    const { data: collection, error: collectionError } = await supabase
      .from('collections')
      .select('user_id')
      .eq('id', data.collection_id)
      .single();

    if (collectionError || !collection) {
      return { success: false, error: 'Collection not found' };
    }

    if (collection.user_id !== user.id) {
      return { success: false, error: 'Unauthorized' };
    }

    const { data: qaPair, error } = await supabase
      .from('qa_pairs')
      .insert({
        question: data.question.trim(),
        answer: data.answer.trim(),
        collection_id: data.collection_id,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating Q&A pair:', error);
      return { success: false, error: 'Failed to create Q&A pair' };
    }

    revalidatePath(`/dashboard/collections/${data.collection_id}`);
    return { success: true, data: qaPair };
  } catch (error) {
    console.error('Error in createQAPair:', error);
    return { success: false, error: 'Something went wrong' };
  }
}

export async function updateQAPair(
  qaPairId: string,
  data: {
    question: string;
    answer: string;
  }
) {
  try {
    const user = await getCurrentUser();

    // First, verify the Q&A pair belongs to a collection owned by the current user
    const { data: qaPair, error: fetchError } = await supabase
      .from('qa_pairs')
      .select(`
        collection_id,
        collections!inner(user_id)
      `)
      .eq('id', qaPairId)
      .single();

    if (fetchError || !qaPair) {
      return { success: false, error: 'Q&A pair not found' };
    }

    if (qaPair.collections.user_id !== user.id) {
      return { success: false, error: 'Unauthorized' };
    }

    const { error } = await supabase
      .from('qa_pairs')
      .update({
        question: data.question.trim(),
        answer: data.answer.trim(),
      })
      .eq('id', qaPairId);

    if (error) {
      console.error('Error updating Q&A pair:', error);
      return { success: false, error: 'Failed to update Q&A pair' };
    }

    revalidatePath(`/dashboard/collections/${qaPair.collection_id}`);
    return { success: true };
  } catch (error) {
    console.error('Error in updateQAPair:', error);
    return { success: false, error: 'Something went wrong' };
  }
}

export async function deleteQAPair(qaPairId: string) {
  try {
    const user = await getCurrentUser();

    // First, verify the Q&A pair belongs to a collection owned by the current user
    const { data: qaPair, error: fetchError } = await supabase
      .from('qa_pairs')
      .select(`
        collection_id,
        collections!inner(user_id)
      `)
      .eq('id', qaPairId)
      .single();

    if (fetchError || !qaPair) {
      return { success: false, error: 'Q&A pair not found' };
    }

    if (qaPair.collections.user_id !== user.id) {
      return { success: false, error: 'Unauthorized' };
    }

    const { error } = await supabase
      .from('qa_pairs')
      .delete()
      .eq('id', qaPairId);

    if (error) {
      console.error('Error deleting Q&A pair:', error);
      return { success: false, error: 'Failed to delete Q&A pair' };
    }

    revalidatePath(`/dashboard/collections/${qaPair.collection_id}`);
    return { success: true };
  } catch (error) {
    console.error('Error in deleteQAPair:', error);
    return { success: false, error: 'Something went wrong' };
  }
}
