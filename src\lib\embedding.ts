'use client';

import { pipeline, Pipeline } from '@xenova/transformers';

// Global variable to store the embedding pipeline
let embeddingPipeline: Pipeline | null = null;

/**
 * Initialize the embedding pipeline
 * Uses a lightweight sentence transformer model for client-side embedding
 */
export async function initializeEmbeddingPipeline(): Promise<Pipeline> {
  if (embeddingPipeline) {
    return embeddingPipeline;
  }

  try {
    console.log('🧠 Initializing embedding model...');

    // Use a lightweight model that works well in browsers
    embeddingPipeline = await pipeline(
      'feature-extraction',
      'Xenova/all-MiniLM-L6-v2',
      {
        // Configure for browser usage
        device: 'webgpu', // Falls back to CPU if WebGPU not available
        dtype: 'fp32',
      }
    );

    console.log('✅ Embedding model initialized successfully');
    return embeddingPipeline;
  } catch (error) {
    console.error('❌ Failed to initialize embedding pipeline:', error);
    throw new Error('Failed to initialize embedding model');
  }
}

/**
 * Generate embeddings for text using the client-side model
 */
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    console.log('🔄 Generating embedding for text:', text.substring(0, 100) + '...');
    const pipeline = await initializeEmbeddingPipeline();

    // Generate embedding
    const result = await pipeline(text, {
      pooling: 'mean',
      normalize: true,
    });

    // Extract the embedding vector
    const embedding = Array.from(result.data) as number[];
    console.log('✅ Generated embedding with dimensions:', embedding.length);

    return embedding;
  } catch (error) {
    console.error('❌ Failed to generate embedding:', error);
    throw new Error('Failed to generate embedding');
  }
}

/**
 * Generate embedding for a Q&A pair by combining question and answer
 */
export async function generateQAEmbedding(question: string, answer: string): Promise<number[]> {
  // Combine question and answer with a separator for better semantic representation
  const combinedText = `Question: ${question}\nAnswer: ${answer}`;
  return generateEmbedding(combinedText);
}

/**
 * Sync Q&A pair with Qdrant
 */
export async function syncToQdrant(data: {
  question: string;
  answer: string;
  vector: number[];
  collection_id: string;
  user_id: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🔄 Syncing to Qdrant:', {
      collection_id: data.collection_id,
      user_id: data.user_id,
      vectorDimensions: data.vector.length,
    });

    const response = await fetch('/api/qdrant-sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to sync with Qdrant');
    }

    console.log('✅ Successfully synced to Qdrant:', result);
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to sync to Qdrant:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete Q&A pair from Qdrant
 */
export async function deleteFromQdrant(data: {
  question: string;
  collection_id: string;
  user_id: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🗑️ Deleting from Qdrant:', {
      collection_id: data.collection_id,
      user_id: data.user_id,
      question: data.question.substring(0, 50) + '...',
    });

    const response = await fetch('/api/qdrant-delete', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      console.warn('⚠️ Qdrant deletion failed:', result.error);
      return {
        success: false,
        error: result.error || 'Failed to delete from Qdrant'
      };
    }

    console.log('✅ Successfully deleted from Qdrant:', result);
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to delete from Qdrant:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
