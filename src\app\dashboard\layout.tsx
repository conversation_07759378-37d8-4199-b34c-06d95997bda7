import { getRequiredSession } from '@/lib/auth';
import { DashboardShell } from '@/components/dashboard/dashboard-shell';
import { ErrorBoundary } from '@/components/error-boundary';

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getRequiredSession();

  return (
    <ErrorBoundary>
      <DashboardShell user={session.user}>
        {children}
      </DashboardShell>
    </ErrorBoundary>
  );
}
