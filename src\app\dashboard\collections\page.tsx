import { getCurrentUser } from '@/lib/auth';
import { supabase } from '@/lib/supabase';
import { CollectionsView } from '@/components/collections/collections-view';
import { Loading } from '@/components/loading';
import { Suspense } from 'react';

async function CollectionsData() {
  const user = await getCurrentUser();

  // Fetch user's collections with Q&A count
  const { data: collections, error } = await supabase
    .from('collections')
    .select(`
      *,
      qa_pairs(count)
    `)
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching collections:', error);
    throw new Error('Failed to load collections');
  }

  return (
    <CollectionsView
      collections={collections || []}
      userId={user.id!}
    />
  );
}

export default function CollectionsPage() {
  return (
    <Suspense fallback={<Loading message="Loading collections..." fullHeight={false} />}>
      <CollectionsData />
    </Suspense>
  );
}
