'use client';

import { signIn, getSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { 
  Container, 
  Paper, 
  Title, 
  Text, 
  Button, 
  Stack, 
  Center,
  Loader
} from '@mantine/core';
import { IconBrandGoogle } from '@tabler/icons-react';
import { APP_CONFIG } from '@/lib/config';

export default function SignIn() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [checkingSession, setCheckingSession] = useState(true);

  useEffect(() => {
    const checkSession = async () => {
      const session = await getSession();
      if (session?.user) {
        router.push(APP_CONFIG.routes.dashboard);
      } else {
        setCheckingSession(false);
      }
    };
    
    checkSession();
  }, [router]);

  const handleGoogleSignIn = async () => {
    setLoading(true);
    try {
      await signIn('google', { 
        callbackUrl: APP_CONFIG.routes.dashboard 
      });
    } catch (error) {
      console.error('Sign in error:', error);
      setLoading(false);
    }
  };

  if (checkingSession) {
    return (
      <Center h="100vh">
        <Loader size="lg" />
      </Center>
    );
  }

  return (
    <Container size="xs" h="100vh" style={{ display: 'flex', alignItems: 'center' }}>
      <Paper shadow="md" p="xl" radius="md" w="100%">
        <Stack gap="lg" align="center">
          <Title order={1} ta="center" c="blue">
            {APP_CONFIG.name}
          </Title>
          
          <Text ta="center" c="dimmed" size="sm">
            {APP_CONFIG.description}
          </Text>
          
          <Text ta="center" size="lg" fw={500}>
            Sign in to your account
          </Text>
          
          <Button
            leftSection={<IconBrandGoogle size={20} />}
            variant="outline"
            size="lg"
            fullWidth
            loading={loading}
            onClick={handleGoogleSignIn}
          >
            Continue with Google
          </Button>
          
          <Text ta="center" size="xs" c="dimmed">
            By signing in, you agree to our terms of service and privacy policy.
          </Text>
        </Stack>
      </Paper>
    </Container>
  );
}
