// Simple database connection test
import { supabase } from './supabase';

export async function testDatabaseConnection() {
  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      console.error('Database connection error:', error);
      return false;
    }

    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('Database test failed:', error);
    return false;
  }
}

export async function testTableStructure() {
  try {
    // Test users table
    const { error: usersError } = await supabase
      .from('users')
      .select('id, email, name, image, created_at')
      .limit(1);

    if (usersError) {
      console.error('Users table error:', usersError);
      return false;
    }

    // Test collections table
    const { error: collectionsError } = await supabase
      .from('collections')
      .select('id, name, description, user_id, created_at')
      .limit(1);

    if (collectionsError) {
      console.error('Collections table error:', collectionsError);
      return false;
    }

    // Test qa_pairs table
    const { error: qaPairsError } = await supabase
      .from('qa_pairs')
      .select('id, question, answer, collection_id, created_at')
      .limit(1);

    if (qaPairsError) {
      console.error('QA pairs table error:', qaPairsError);
      return false;
    }

    console.log('✅ All database tables are properly structured');
    return true;
  } catch (error) {
    console.error('Table structure test failed:', error);
    return false;
  }
}
