@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Fix for large chevron arrow blocking interface - only target accordion chevrons */
.mantine-Accordion-chevron {
  display: none !important;
}

/* Hide any problematic large arrows or chevrons - but not in menus */
[data-chevron="true"]:not(.mantine-Menu-target *) {
  display: none !important;
}

/* Ensure no large pseudo-elements are blocking the interface - but allow normal icons */
body *::before,
body *::after {
  max-width: 100px !important;
  max-height: 100px !important;
}

/* Ensure profile menu chevron is properly sized */
.mantine-Menu-target .tabler-icon {
  width: 14px !important;
  height: 14px !important;
}
