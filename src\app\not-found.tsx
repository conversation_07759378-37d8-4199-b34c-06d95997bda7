import { Container, Title, Text, But<PERSON>, Stack, Card } from '@mantine/core';
import { IconError404 } from '@tabler/icons-react';
import Link from 'next/link';
import { APP_CONFIG } from '@/lib/config';

export default function NotFound() {
  return (
    <Container size="sm" py="xl" h="100vh" style={{ display: 'flex', alignItems: 'center' }}>
      <Card shadow="sm" padding="xl" radius="md" withBorder w="100%">
        <Stack align="center" gap="lg">
          <IconError404 size={64} color="var(--mantine-color-dimmed)" />
          <div style={{ textAlign: 'center' }}>
            <Title order={1} mb="sm">Page Not Found</Title>
            <Text c="dimmed" size="lg" mb="md">
              The page you're looking for doesn't exist or you don't have permission to access it.
            </Text>
          </div>
          <Button component={Link} href={APP_CONFIG.routes.dashboard}>
            Go to Dashboard
          </Button>
        </Stack>
      </Card>
    </Container>
  );
}
