import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    message: 'Embedding test endpoint ready',
    instructions: 'POST to this endpoint with { "question": "test question", "answer": "test answer" } to test client-side embedding',
  });
}

export async function POST(request: Request) {
  try {
    const { question, answer } = await request.json();
    
    if (!question || !answer) {
      return NextResponse.json(
        { error: 'Both question and answer are required' },
        { status: 400 }
      );
    }

    // This endpoint is just for testing - the actual embedding happens client-side
    return NextResponse.json({
      success: true,
      message: 'Test data received successfully',
      data: {
        question,
        answer,
        combinedText: `Question: ${question}\nAnswer: ${answer}`,
        note: 'Actual embedding happens client-side using @xenova/transformers',
      },
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid JSON data' },
      { status: 400 }
    );
  }
}
