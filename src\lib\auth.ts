import 'server-only';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { APP_CONFIG } from './config';

export async function getRequiredSession() {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect(APP_CONFIG.routes.login);
  }

  return session;
}

export async function getCurrentUser() {
  const session = await getRequiredSession();
  return session.user;
}