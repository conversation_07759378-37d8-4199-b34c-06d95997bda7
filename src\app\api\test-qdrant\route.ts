import { NextResponse } from 'next/server';
import { QdrantClient } from '@qdrant/js-client-rest';

export async function GET() {
  // Check if QDRANT_URL is configured
  if (!process.env.QDRANT_URL) {
    return NextResponse.json({
      success: false,
      error: 'QDRANT_URL environment variable not configured',
      details: 'Qdrant is optional - the app works without it',
      qdrantUrl: null,
      hasApiKey: false,
      configured: false,
    }, { status: 400 });
  }

  try {
    // Initialize Qdrant client
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    // Test connection by getting collections
    const collections = await qdrantClient.getCollections();

    return NextResponse.json({
      success: true,
      message: 'Qdrant connection successful',
      collections: collections.collections?.map(col => ({
        name: col.name,
        status: col.status,
        vectors_count: col.vectors_count,
      })) || [],
      qdrantUrl: process.env.QDRANT_URL,
      hasApiKey: !!process.env.QDRANT_API_KEY,
      configured: true,
    });

  } catch (error) {
    console.error('Qdrant connection test failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to connect to Qdrant',
      details: error instanceof Error ? error.message : 'Unknown error',
      qdrantUrl: process.env.QDRANT_URL,
      hasApiKey: !!process.env.QDRANT_API_KEY,
      configured: true,
    }, { status: 500 });
  }
}
