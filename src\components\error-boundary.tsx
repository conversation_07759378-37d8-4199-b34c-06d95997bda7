'use client';

import React from 'react';
import { Container, Title, Text, But<PERSON>, Stack, Card } from '@mantine/core';
import { IconAlertTriangle } from '@tabler/icons-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />;
      }

      return <DefaultErrorFallback error={this.state.error} resetError={this.resetError} />;
    }

    return this.props.children;
  }
}

function DefaultErrorFallback({ error, resetError }: { error?: Error; resetError: () => void }) {
  return (
    <Container size="sm" py="xl">
      <Card shadow="sm" padding="xl" radius="md" withBorder>
        <Stack align="center" gap="lg">
          <IconAlertTriangle size={64} color="var(--mantine-color-red-6)" />
          <div style={{ textAlign: 'center' }}>
            <Title order={2} mb="sm">Something went wrong</Title>
            <Text c="dimmed" size="lg" mb="md">
              We encountered an unexpected error. Please try again.
            </Text>
            {process.env.NODE_ENV === 'development' && error && (
              <Text size="sm" c="red" style={{ fontFamily: 'monospace', textAlign: 'left' }}>
                {error.message}
              </Text>
            )}
          </div>
          <Button onClick={resetError}>
            Try Again
          </Button>
        </Stack>
      </Card>
    </Container>
  );
}
